{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"enterpriseScaleCompanyPrefix": {"value": null}, "singlePlatformSubscriptionId": {"value": null}, "enablePrivateSubnet": {"value": "Audit"}, "telemetryOptOut": {"value": "No"}, "enableLogAnalytics": {"value": "Yes"}, "laCategory": {"value": "allLogs"}, "retentionInDays": {"value": "30"}, "securitySubscriptionId": {"value": null}, "managementSubscriptionId": {"value": null}, "enableChangeTracking": {"value": "Yes"}, "enableUpdateMgmt": {"value": "Yes"}, "enableVmInsights": {"value": "Yes"}, "enableAsc": {"value": "Yes"}, "emailContactAsc": {"value": null}, "enableAscForServers": {"value": "DeployIfNotExists"}, "enableAscForServersVulnerabilityAssessments": {"value": "DeployIfNotExists"}, "enableAscForOssDb": {"value": "DeployIfNotExists"}, "enableAscForCosmosDbs": {"value": "DeployIfNotExists"}, "enableAscForAppServices": {"value": "DeployIfNotExists"}, "enableAscForStorage": {"value": "DeployIfNotExists"}, "enableAscForSql": {"value": "DeployIfNotExists"}, "enableAscForSqlOnVm": {"value": "DeployIfNotExists"}, "enableAscForKeyVault": {"value": "DeployIfNotExists"}, "enableAscForArm": {"value": "DeployIfNotExists"}, "enableAscForApis": {"value": "DeployIfNotExists"}, "enableAscForCspm": {"value": "DeployIfNotExists"}, "enableAscForContainers": {"value": "DeployIfNotExists"}, "enableMDEndpoints": {"value": "DeployIfNotExists"}, "enableSecuritySolution": {"value": null}, "enableServiceHealth": {"value": null}, "enableServiceHealthBuiltIn": {"value": "Yes"}, "enableMonitorBaselines": {"value": "Yes"}, "enableMonitorConnectivity": {"value": "Yes"}, "enableMonitorIdentity": {"value": "Yes"}, "enableMonitorManagement": {"value": "Yes"}, "monitorAlertsResourceGroup": {"value": "rg-alz-monitoring-001"}, "userAssignedManagedIdentityName": {"value": "id-amba-prod-001"}, "ambaAgEmailContact": {"value": null}, "ambaAgServiceHook": {"value": null}, "ambaAgArmRole": {"value": ["8e3af657-a8ff-443c-a75c-2fe8c4bcb635"]}, "enableAMBAHybridVM": {"value": "Yes"}, "enableAMBAKeyManagement": {"value": "Yes"}, "enableAMBALoadBalancing": {"value": "Yes"}, "enableAMBANetworkChanges": {"value": "Yes"}, "enableAMBARecoveryServices": {"value": "Yes"}, "enableAMBAStorage": {"value": "Yes"}, "enableAMBAVM": {"value": "Yes"}, "enableAMBAWeb": {"value": "Yes"}, "connectivitySubscriptionId": {"value": null}, "addressPrefix": {"value": null}, "connectivityLocation": {"value": null}, "deployAVNM": {"value": null}, "enableDdoS": {"value": null}, "enablePrivateDnsZones": {"value": null}, "privateDnsZonesToDeploy": {"value": null}, "enableVpnGw": {"value": null}, "enableVpnActiveActive": {"value": null}, "gwRegionalOrAz": {"value": null}, "gwRegionalSku": {"value": null}, "gwAzSku": {"value": null}, "vpnGateWayScaleUnit": {"value": null}, "subnetMaskForGw": {"value": null}, "enableErGw": {"value": null}, "erAzSku": {"value": null}, "erRegionalSku": {"value": null}, "erRegionalOrAz": {"value": null}, "expressRouteScaleUnit": {"value": null}, "enableHub": {"value": "No"}, "enableAzFw": {"value": null}, "enableAzFwDnsProxy": {"value": null}, "firewallSku": {"value": null}, "firewallZones": {"value": null}, "subnetMaskForAzFw": {"value": null}, "subnetMaskForAzFwMgmt": {"value": null}, "enablevWANRoutingIntent": {"value": null}, "internetTrafficRoutingPolicy": {"value": null}, "privateTrafficRoutingPolicy": {"value": null}, "vWANHubRoutingPreference": {"value": null}, "vWANHubCapacity": {"value": null}, "addressPrefixSecondary": {"value": null}, "connectivityLocationSecondary": {"value": null}, "enablePrivateDnsZonesSecondary": {"value": "No"}, "privateDnsZonesToDeploySecondary": {"value": null}, "enableVpnGwSecondary": {"value": null}, "enableVpnActiveActiveSecondary": {"value": null}, "gwRegionalOrAzSecondary": {"value": null}, "gwRegionalSkuSecondary": {"value": null}, "gwAzSkuSecondary": {"value": null}, "vpnGateWayScaleUnitSecondary": {"value": null}, "subnetMaskForGwSecondary": {"value": null}, "enableErGwSecondary": {"value": null}, "erAzSkuSecondary": {"value": null}, "erRegionalSkuSecondary": {"value": null}, "erRegionalOrAzSecondary": {"value": null}, "expressRouteScaleUnitSecondary": {"value": null}, "enableSecondaryRegion": {"value": "Yes"}, "enableHubSecondary": {"value": "No"}, "enableAzFwSecondary": {"value": null}, "enableAzFwDnsProxySecondary": {"value": null}, "firewallSkuSecondary": {"value": null}, "firewallZonesSecondary": {"value": null}, "subnetMaskForAzFwSecondary": {"value": null}, "subnetMaskForAzFwMgmtSecondary": {"value": null}, "enablevWANRoutingIntentSecondary": {"value": null}, "internetTrafficRoutingPolicySecondary": {"value": null}, "privateTrafficRoutingPolicySecondary": {"value": null}, "vWANHubRoutingPreferenceSecondary": {"value": null}, "vWANHubCapacitySecondary": {"value": null}, "identitySubscriptionId": {"value": null}, "denyMgmtPortsForIdentity": {"value": "Yes"}, "denySubnetWithoutNsgForIdentity": {"value": "Yes"}, "denyPipForIdentity": {"value": "Yes"}, "enableVmBackupForIdentity": {"value": "Yes"}, "identityAddressPrefix": {"value": null}, "identityAddressPrefixSecondary": {"value": null}, "corpConnectedLzSubscriptionId": {"value": null}, "corpLzSubscriptionId": {"value": []}, "onlineLzSubscriptionId": {"value": []}, "enableLzDdoS": {"value": null}, "denyPublicEndpoints": {"value": "Yes"}, "denyPipOnNicForCorp": {"value": "Yes"}, "enablePrivateDnsZonesForLzs": {"value": null}, "enableEncryptionInTransit": {"value": "Yes"}, "enableVmMonitoring": {"value": "Yes"}, "enableVmssMonitoring": {"value": "Yes"}, "enableVmHybridMonitoring": {"value": "Yes"}, "denyAksPrivileged": {"value": "Yes"}, "denyAksPrivilegedEscalation": {"value": "Yes"}, "denyClassicResources": {"value": "Yes"}, "denyVMUnmanagedDisk": {"value": "Yes"}, "denyHttpIngressForAks": {"value": "Yes"}, "enableVmBackup": {"value": "Yes"}, "denyMgmtPorts": {"value": "Yes"}, "denySubnetWithoutNsg": {"value": "Yes"}, "denyIpForwarding": {"value": "Yes"}, "enableSqlEncryption": {"value": "Yes"}, "enableSqlThreat": {"value": "Yes"}, "enableDecommissioned": {"value": "Yes"}, "enableSandbox": {"value": "Yes"}, "enableSqlAudit": {"value": "Yes"}, "enableStorageHttps": {"value": "Yes"}, "enforceKvGuardrails": {"value": "Yes"}, "enforceBackup": {"value": "Yes"}, "enforceKvGuardrailsPlat": {"value": "Yes"}, "enforceBackupPlat": {"value": "Yes"}, "enableGuestAttestationPlat": {"value": "Yes"}, "enableGuestAttestationLZ": {"value": "Yes"}, "denyHybridNetworking": {"value": "Yes"}, "auditPeDnsZones": {"value": "Yes"}, "auditAppGwWaf": {"value": "Yes"}, "enforceAcsb": {"value": "Yes"}, "delayCount": {"value": null}, "currentDateTimeUtcNow": {"value": null}, "enableWsCMKInitiatives": {"value": "Audit"}, "wsCMKSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsAPIMInitiatives": {"value": "Audit"}, "wsAPIMSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsAppServicesInitiatives": {"value": "Audit"}, "wsAppServicesSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsAutomationInitiatives": {"value": "Audit"}, "wsAutomationSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsBotServiceInitiatives": {"value": "Audit"}, "wsBotServiceSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsCognitiveServicesInitiatives": {"value": "Audit"}, "wsCognitiveServicesSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsComputeInitiatives": {"value": "Audit"}, "wsComputeSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsContainerAppsInitiatives": {"value": "Audit"}, "wsContainerAppsSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsContainerInstanceInitiatives": {"value": "Audit"}, "wsContainerInstanceSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsContainerRegistryInitiatives": {"value": "Audit"}, "wsContainerRegistrySelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsCosmosDbInitiatives": {"value": "Audit"}, "wsCosmosDbSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsDataExplorerInitiatives": {"value": "Audit"}, "wsDataExplorerSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsDataFactoryInitiatives": {"value": "Audit"}, "wsDataFactorySelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsEventGridInitiatives": {"value": "Audit"}, "wsEventGridSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsEventHubInitiatives": {"value": "Audit"}, "wsEventHubSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsKeyVaultSupInitiatives": {"value": "Audit"}, "wsKeyVaultSupSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsKubernetesInitiatives": {"value": "Audit"}, "wsKubernetesSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsMachineLearningInitiatives": {"value": "Audit"}, "wsMachineLearningSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsMySQLInitiatives": {"value": "Audit"}, "wsMySQLSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsNetworkInitiatives": {"value": null}, "wsNetworkSelectorMG": {"value": null}, "enableWsOpenAIInitiatives": {"value": "Audit"}, "wsOpenAISelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsPostgreSQLInitiatives": {"value": "Audit"}, "wsPostgreSQLSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsServiceBusInitiatives": {"value": "Audit"}, "wsServiceBusSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsSQLInitiatives": {"value": "Audit"}, "wsSQLSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsStorageInitiatives": {"value": "Audit"}, "wsStorageSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsSynapseInitiatives": {"value": "Audit"}, "wsSynapseSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "enableWsVirtualDesktopInitiatives": {"value": "Audit"}, "wsVirtualDesktopSelectorMG": {"value": ["contoso-platform", "contoso-landingzones"]}, "regulatoryComplianceInitativesToAssign": {"value": null}, "regCompPolParAusGovIsmRestrictedVmAdminsExclude": {"value": null}, "regCompPolParAusGovIsmRestrictedResourceTypes": {"value": null}, "regCompPolParMPAACertificateThumb": {"value": null}, "regCompPolParMPAAApplicationName": {"value": null}, "regCompPolParMPAAStoragePrefix": {"value": null}, "regCompPolParMPAAResGroupPrefix": {"value": null}, "regCompPolParMPAARBatchMetricName": {"value": null}, "regCompPolParSovBaseConfRegions": {"value": null}, "regCompPolParSovBaseGlobalRegions": {"value": null}, "regCompPolParSwift2020VmAdminsInclude": {"value": null}, "regCompPolParSwift2020DomainFqdn": {"value": null}, "regCompPolParCanadaFedPbmmVmAdminsInclude": {"value": null}, "regCompPolParCanadaFedPbmmVmAdminsExclude": {"value": null}, "regCompPolParCisV2KeyVaultKeysRotateDays": {"value": 90}, "regCompPolParCmmcL3VmAdminsInclude": {"value": null}, "regCompPolParCmmcL3VmAdminsExclude": {"value": null}, "regCompPolParHitrustHipaaApplicationName": {"value": null}, "regCompPolParHitrustHipaaStoragePrefix": {"value": null}, "regCompPolParHitrustHipaaResGroupPrefix": {"value": null}, "regCompPolParHitrustHipaaCertificateThumb": {"value": null}, "regCompPolParIrs1075Sep2016VmAdminsExclude": {"value": null}, "regCompPolParIrs1075Sep2016VmAdminsInclude": {"value": null}, "regCompPolParNZIsmRestrictedVmAdminsInclude": {"value": null}, "regCompPolParNZIsmRestrictedVmAdminsExclude": {"value": null}, "regCompPolParNistSp800171R2VmAdminsExclude": {"value": null}, "regCompPolParNistSp800171R2VmAdminsInclude": {"value": null}, "regCompPolParSoc2Type2AllowedRegistries": {"value": null}, "regCompPolParSoc2Type2MaxCpuUnits": {"value": null}, "regCompPolParSoc2Type2MaxMemoryBytes": {"value": null}, "listOfResourceTypesDisallowedForDeletion": {"value": null}}}