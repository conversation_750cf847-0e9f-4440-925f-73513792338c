{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."}}}, "variables": {"managementGroups": {"platformManagement": "mg-Platform-Management", "platformConnectivity": "mg-Platform-Connectivity", "ldzPrd": "ldz-prd", "ldzPrdLegacy": "ldz-prd-legacy", "ldzPrdMicrosvs": "ldz-prd-microsvs", "ldzNonPrd": "ldz-non-prd", "ldzNonPrdUat": "ldz-non-prd-uat", "ldzNonPrdDev": "ldz-non-prd-dev", "sandbox": "Sandbox", "decommissioned": "Decommissioned"}}, "resources": [{"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').platformManagement]", "properties": {"displayName": "Platform Management"}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').platformConnectivity]", "properties": {"displayName": "Platform Connectivity", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').ldzPrd]", "properties": {"displayName": "Landing Zone Production", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').ldzPrdLegacy]", "properties": {"displayName": "Landing Zone Production Legacy", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzPrd)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzPrd)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').ldzPrdMicrosvs]", "properties": {"displayName": "Landing Zone Production Microservices", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzPrd)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzPrd)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').ldzNonPrd]", "properties": {"displayName": "Landing Zone Non-Production", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').ldzNonPrdUat]", "properties": {"displayName": "Landing Zone Non-Production UAT", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzNonPrd)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzNonPrd)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').ldzNonPrdDev]", "properties": {"displayName": "Landing Zone Non-Production Development", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzNonPrd)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').ldzNonPrd)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').sandbox]", "properties": {"displayName": "Sandbox", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[variables('managementGroups').decommissioned]", "properties": {"displayName": "Decommissioned", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"}}}, "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups', variables('managementGroups').platformManagement)]"]}], "outputs": {"managementGroupIds": {"type": "object", "value": "[variables('managementGroups')]"}}}