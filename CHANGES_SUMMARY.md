# Tóm tắt thay đổi cấu trúc Management Group

## Cấu trúc mới theo yêu cầu

Dựa trên ảnh được cung cấp, cấu trúc Management Group đã được sửa đổi như sau:

### Cấ<PERSON> trú<PERSON> cũ (Enterprise Scale Landing Zone mặc định):
```
[prefix]-root
├── [prefix]-platform
│   ├── [prefix]-management
│   ├── [prefix]-connectivity
│   ├── [prefix]-identity
│   └── [prefix]-security
├── [prefix]-landingzones
│   ├── [prefix]-corp
│   └── [prefix]-online
├── [prefix]-sandboxes
└── [prefix]-decommissioned
```

### Cấu trúc mới (theo yê<PERSON> cầ<PERSON>):
```
mg-Platform-Management (Root)
├── Platform
│   ├── mg-Platform-Management
│   └── mg-Platform-Connectivity
├── Landing Zone
│   ├── ldz-prd
│   │   ├── ldz-prd-legacy
│   │   └── ldz-prd-microsvs
│   └── ldz-non-prd
│       ├── ldz-non-prd-uat
│       └── ldz-non-prd-dev
├── Sandbox
└── Decommissioned
```

## Các file đã được sửa đổi:

### 1. template.json
- **Phần variables.mgmtGroups**: Cập nhật tên management groups từ dạng động (dựa trên prefix) sang tên cố định
- **Phần variables.mgmtGroupsLite**: Cập nhật cho phiên bản lite
- **Phần variables.scopes**: Thêm các scope mới cho management groups con

### 2. managementGroupTemplates/mgmtGroupStructure/mgmtGroups.json (Mới)
- Tạo file template mới để định nghĩa cấu trúc management group
- Định nghĩa các management group với tên cố định
- Thiết lập quan hệ parent-child theo cấu trúc mới

### 3. managementGroupTemplates/mgmtGroupStructure/mgmtGroupsLite.json (Mới)
- Phiên bản đơn giản hóa của cấu trúc management group
- Chỉ bao gồm các management group cơ bản

## Các thay đổi chính:

1. **Root Management Group**: Thay đổi từ `[prefix]` sang `mg-Platform-Management`
2. **Platform Groups**: 
   - Management: `mg-Platform-Management`
   - Connectivity: `mg-Platform-Connectivity`
3. **Landing Zone Groups**:
   - Production: `ldz-prd` với sub-groups `ldz-prd-legacy`, `ldz-prd-microsvs`
   - Non-Production: `ldz-non-prd` với sub-groups `ldz-non-prd-uat`, `ldz-non-prd-dev`
4. **Sandbox**: `Sandbox`
5. **Decommissioned**: `Decommissioned`

## Lưu ý quan trọng:

1. **Tên cố định**: Các management group giờ sử dụng tên cố định thay vì dựa trên parameter `enterpriseScaleCompanyPrefix`
2. **Cấu trúc phân cấp**: Cấu trúc mới có nhiều level con hơn, đặc biệt trong Landing Zone
3. **Tương thích**: Template vẫn giữ nguyên các tham chiếu đến `topLevelManagementGroupPrefix` trong các policy assignments để đảm bảo tương thích

## Cách sử dụng:

1. Deploy template với parameter `enterpriseScaleCompanyPrefix` (vẫn cần thiết cho các policy assignments)
2. Cấu trúc management group sẽ được tạo theo tên cố định như đã định nghĩa
3. Các subscription có thể được đặt vào các management group tương ứng theo mục đích sử dụng

## Kiểm tra sau khi deploy:

1. Xác nhận cấu trúc management group trong Azure Portal
2. Kiểm tra các policy assignments đã được áp dụng đúng scope
3. Verify subscription placement trong các management group phù hợp
